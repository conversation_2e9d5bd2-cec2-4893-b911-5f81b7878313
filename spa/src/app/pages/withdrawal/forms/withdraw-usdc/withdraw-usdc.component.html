<ons-list-item class="m-b-10" expandable info="usdc">
  <div class="card-wrapper sm-text-container">
    <ons-row>
      <ons-col>
        <img src="/assets/img/gateway-usdc-logo.png" alt="USDC">
      </ons-col>
      <ons-col align="center"></ons-col>
    </ons-row>
    <ons-row class="info-content">
      <ons-col class="col" align="center">
        <div class="col-content left">
          <ons-col class="m-b-5">
            <div class="flex payment-option-details">
              <div>{{ 'transaction.processing_time' | translate }}:</div>
              <div class="font-strong">{{ utils.bundleFieldValue('processing_time') | translate }}</div>
            </div>
          </ons-col>
          <ons-col>
            <div class="flex payment-option-details">
              <div>{{ 'transaction.fee' |translate }}:</div>
              <div class="font-strong">{{ state.fee }}</div>
            </div>
          </ons-col>
        </div>
      </ons-col>
      <ons-col class="col" align="center">
        <div class="col-content left">
          <ons-col class="m-b-5">
            <div class="flex payment-option-details">
              <div>{{ 'transaction.min_withdrawal' |translate }}:</div>
              <div class="font-strong" *ngIf="state.adjustedExchangeRates">{{ state.adjustedExchangeRates.limits.minimumAmountWithdrawal }} USDC</div>
            </div>
          </ons-col>
          <ons-col>
            <div class="flex payment-option-details">
              <div>{{ 'transaction.max_withdrawal' |translate }}:</div>
              <div class="font-strong" *ngIf="state.adjustedExchangeRates">{{ state.adjustedExchangeRates.limits.maximumAmountWithdrawal }} USDC</div>
            </div>
          </ons-col>
        </div>
      </ons-col>
    </ons-row>
  </div>
  <div class="expandable-content p-0">
    <div class="gateway-form">
      <ons-row class="bg-usdc amount-wrapper white m-b-20">
        <!-- Exchange Rate -->
        <div class="exchange-rate-expandable w-100 m-t-5 m-b-10" translate="no">
          <span class="font-xxs">
            <i class="fa-solid fa-right-left-large font-xxs"></i>
            {{ 'transaction.exchange_rate' |translate }}: &nbsp;<strong id="currentRate" *ngIf="state.adjustedExchangeRates">1 USDC = {{ state.adjustedUsdcValue }} EUR</strong>
          </span>
        </div>
        <ons-col width="60%">
          <p class="font-xxs">{{ 'transaction.total_eur_deducted' |translate }}:</p>
        </ons-col>
        <ons-col width="40%" translate="no">
          <p class="font-bold font-xxs text-right">{{ state.totalAmount }} EUR</p>
        </ons-col>
        <ons-col width="60%">
          <p class="font-xxs">{{ 'transaction.fee' |translate }}:</p>
        </ons-col>
        <ons-col width="40%" translate="no">
          <p class="font-bold font-xxs text-right">{{ state.actualFee }} EUR</p>
        </ons-col>
        <hr class="m-t-10">
        <ons-col width="60%">
          <p class="font-xxs font-uppercase">{{ 'transaction.total_usdc_received' |translate }}:</p>
        </ons-col>
        <ons-col width="40%" translate="no">
          <p class="font-strong font-xs text-right">{{ state.totalUsdcAmount }} USDC</p>
        </ons-col>
      </ons-row>

      <ons-row class="gateway-wrap">
        <form *ngIf="state.form" [formGroup]="state.form" style="width: 100%;">
          <div class="amount-container m-b-10">
            <ng-container formArrayName="subTransactions">
              <div *ngFor="let subTransaction of state.form.controls.subTransactions.controls; let i = index">
                <ng-container [formGroupName]="i">
                  <div class="amount-container m-b-15">
                    <div class="transaction-select m-b-15" [ngClass]="{'has-error': subTransaction.get('username').invalid && (subTransaction.get('username').dirty || subTransaction.get('username').touched)}">
                      <input formControlName="productCode" type="hidden">
                      <label class="font-strong font-xxss font-uppercase">{{ 'label.select_product' |translate }}</label>
                      <select class="w-100 product-option" id="usdc-customer-product" formControlName="username" #username (change)="onProductChange(username.value, oldvalue, i, $event);oldvalue=username.value" translate="no">
                        <option value="" disabled selected>{{ 'Please select a product' | translate }}</option>
                        <ng-container *ngFor="let customerProduct of state.customerProducts">
                          <option *ngIf="customerProduct.product.name !== 'PIWIXchange-EWL' && (customerProduct.product.name !== 'PIWI Slots' || configService.showWhitecliffFeatures)" [value]="customerProduct.username" class="notranslate" [attr.product-name]="customerProduct.product.name">
                            {{ customerProduct.product.name }}
                          </option>
                        </ng-container>
                      </select>
                      <div *ngIf="subTransaction.get('username').invalid && (subTransaction.get('username').dirty || subTransaction.get('username').touched)" class="error-message">
                        <span *ngIf="subTransaction.get('username').errors.required">{{ 'Product is required' |translate }}</span>
                      </div>
                    </div>

                    <label class="font-strong font-xxss font-uppercase m-b-15">{{ 'alike.amount' |translate }}</label>
                    <div class="flex column-form-container--amount w-100">
                      <div [ngClass]="{'w-50': true, 'has-error': subTransaction.get('usdcAmount').invalid && (subTransaction.get('usdcAmount').dirty || subTransaction.get('usdcAmount').touched)}">
                        <label class="amount-label font-strong font-md dark-green">USDC</label>
                        <input decimal formControlName="usdcAmount" id="usdc-input-field" type="text" class="text-input" placeholder="{{ 'Enter USDC amount' | translate }}" (input)="onAmountChange('usdcAmount')">
                        <div *ngIf="subTransaction.get('usdcAmount').invalid && (subTransaction.get('usdcAmount').dirty || subTransaction.get('usdcAmount').touched)" class="error-message">
                          <span *ngIf="subTransaction.get('usdcAmount').errors.required">{{ 'USDC amount is required' |translate }}</span>
                          <span *ngIf="subTransaction.get('usdcAmount').errors.min">{{ 'Minimum amount is' |translate }} {{ minAmount }} USDC</span>
                          <span *ngIf="subTransaction.get('usdcAmount').errors.max">{{ 'Maximum amount is' |translate }} {{ maxAmount }} USDC</span>
                        </div>
                      </div>
                      <div class="flex-vhcenter exchange-icon bg-dark-green">
                        <i class="fa-solid fa-right-left-large font-xxs white"></i>
                      </div>
                      <div [ngClass]="{'w-50': true, 'has-error': subTransaction.get('amount').invalid && (subTransaction.get('amount').dirty || subTransaction.get('amount').touched)}">
                        <label class="amount-label font-strong font-md dark-green">EUR</label>
                        <input decimal twoDecimal formControlName="amount" #amount id="amount" type="text" class="text-input" placeholder="{{ 'Enter EUR amount' | translate }}" (input)="onAmountChange('amount')" pattern="[0-9 .]*">
                        <div  *ngIf="subTransaction.get('amount').invalid && (subTransaction.get('amount').dirty || subTransaction.get('amount').touched)" class="error-message">
                          <span *ngIf="subTransaction.get('amount').errors.required">{{ 'EUR amount is required' |translate }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-container>
              </div>
            </ng-container>
            <div class="m-b-10" [ngClass]="{'has-error': state.form.get('usdcAddress').invalid && (state.form.get('usdcAddress').dirty || state.form.get('usdcAddress').touched)}">
              <input formControlName="usdcAddress" id="usdc-address-input-field" type="text" class="text-input m-t-1" placeholder="{{ 'Enter USDC receiver address' |translate }}">
              <div *ngIf="state.form.get('usdcAddress').invalid && (state.form.get('usdcAddress').dirty || state.form.get('usdcAddress').touched)" class="error-message">
                <span *ngIf="state.form.get('usdcAddress').errors.required">{{ 'USDC Receiver Address is required' |translate }}</span>
              </div>
            </div>

            <!-- OTP -->
            <div class="address-container m-t-10 m-b-5" [ngClass]="{'has-error': state.form.get('verificationCode').invalid && (state.form.get('verificationCode').dirty || state.form.get('verificationCode').touched)}">
              <input formControlName="verificationCode" type="number" class="text-input" placeholder="{{ activeOTPMethod === 'gauth' && totpStatus === 'enabled' ? ('google_auth.withdrawal.placeholder' |translate) : ('Enter verification code' |translate) }}">
              <ons-button *ngIf="activeOTPMethod === 'email'" class="btn btn-copy bg-yellow dark text-center font-xs font-bold m-t-5" (click)="getCode()">{{ 'action.get_code' |translate }}</ons-button>
            </div>
            <!-- End OTP -->

            <!-- Error Message -->
            <div *ngIf="state.form.get('verificationCode').invalid && (state.form.get('verificationCode').dirty || state.form.get('verificationCode').touched)" class="error-message">
              <span *ngIf="state.form.get('verificationCode').errors.required">{{ 'Verification code is required' |translate }}</span>
              <span *ngIf="state.form.get('verificationCode').errors.minLength">{{ 'alike.invalid_code' |translate }}</span>
              <span *ngIf="state.form.get('verificationCode').errors.maxLength">{{ 'alike.invalid_code' |translate }}</span>
            </div>
            <!-- End Error Message -->

            <!-- Switch -->
            <div *ngIf="totpStatus === 'enabled'" (click)="switchOTPMethod()" class="cursor-pointer font-xxs font-bold blue font-underline">
              <span *ngIf="activeOTPMethod !== 'gauth'; else showEmailSwitch">{{ 'google_auth.withdrawal.switch_gauth' |translate }}</span>
              <ng-template #showEmailSwitch>
                <span>{{ 'google_auth.withdrawal.switch_email_otp' |translate }}</span>
              </ng-template>
            </div>
            <!-- End Switch -->

          </div>
          <div class="btn-container w-100 flex m-b-15">
            <ons-button (click)="clear()" class="w-50 btn bg-gray gray text-center font-xs font-bold m-r-10">{{ 'transaction.clear' |translate }}</ons-button>
            <ons-button class="w-50 btn bg-green text-center font-xs font-bold" (click)="onSubmit()" [disabled]="state.form.invalid">{{ 'Request' |translate }}</ons-button>
          </div>
        </form>
      </ons-row>
    </div>
  </div>
</ons-list-item>
