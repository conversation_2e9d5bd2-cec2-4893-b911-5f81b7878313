import { Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import transactionUtil from "../../../../shared/utils/transaction.util";
import {TranslateService} from "@ngx-translate/core";
import {UserService} from "../../../../shared/services/user.service";
import {TransactionApi} from "../../../../shared/api/service/transaction.api";
import {MwaApiService} from "../../../../shared/services/mwa-api.service";
import {EventDispatcherService} from "../../../../shared/event/event-dispatcher.service";
import {HttpClient} from "@angular/common/http";
import {FormArray, FormBuilder, FormGroup, Validators} from "@angular/forms";
import {of, Subject, timer} from "rxjs";
import {delay, filter, mergeMap, take, takeUntil, tap} from "rxjs/operators";
import {inBetween, plus, toFixed} from "../../../../shared/utils/decimal.util";
import {
  Decimal,
  EUR_DECIMAL_PLACES,
  USDT_DECIMAL_PLACES, USDT_RATE_DECIMAL_PLACES
} from "../../../../shared/utils/number.utils";
import {ProductStatusChangedEvent} from "../../../../shared/event/product-status-changed.event";
import {productName} from "../../../../shared/utils/products.util";
import { ConfigService } from 'src/app/shared/services/config.service';
import { OTPMethod, TotpStatus } from 'src/app/shared/api/service/totp.api';

@Component({
  selector: 'app-withdraw-usdc',
  templateUrl: './withdraw-usdc.component.html',
  styleUrls: ['./withdraw-usdc.component.css']
})

export class WithdrawUsdcComponent implements OnInit, OnDestroy {
  static PAYMENT_OPTION = 'USDC';
  static OTP_PURPOSE = 'transaction';

  @Input() changeOTPMethod: () => void;
  @Input() activeOTPMethod: OTPMethod = 'email';
  @Input() totpStatus: TotpStatus = '';
  @Input() user: any;
  @Input() formDetails: any;
  @Input() productBalance: any;
  @Output() submit: EventEmitter<any> = new EventEmitter<any>();
  @Output() submitCode: EventEmitter<any> = new EventEmitter<any>();
  accessToken: string;
  customerProducts: any = {};
  totalUsdcAmount: Decimal;
  totalEurAmount: Decimal;
  blockchainDetails: any = {
    fixedAdjustment: '',
    adjustmentType: '',
    rates: ''
  };
  state: any = {};
  utils: any;
  unsubscribe$ = new Subject<any>();
  minAmount: any = false;
  maxAmount: any = false;

  constructor(
    public configService: ConfigService,
    private translate: TranslateService,
    private userService: UserService,
    private transactionApi: TransactionApi,
    private mwaApiService: MwaApiService,
    private dispatcherService: EventDispatcherService,
    private http: HttpClient,
    private fb: FormBuilder
  ) { }

  ngOnInit() {
    this.translate.use(this.userService.getLocale())
    this.state = {
      adjustedExchangeRates: null,
      adjustedUsdcValue: null,
      customerProducts: this.user.products.filter((customerProduct) => customerProduct.is_active),
      totalAmount: '0.00',
      totalUsdcAmount: '0.00',
      fee: '',
      actualFee: '0.00',
      companyFee: '0.00',
      transactionHash: ''
    };
    this.utils = transactionUtil(this.formDetails.bundle, this.formDetails.fees);
    this.updateFees();

    timer(1000, 1000).pipe(
      takeUntil(this.unsubscribe$),
      filter(seconds => seconds % 60 === 0),
      mergeMap(() => this.mwaApiService.getRates('USDC', 'EUR'))
    ).subscribe((rates: any) => {
      this.state.adjustedExchangeRates = rates;
      this.state.adjustedUsdcValue = toFixed(
        this.getAdjustedRate('usdcAmount', this.state.totalAmount),
        USDT_RATE_DECIMAL_PLACES
      );

      if (this.maxAmount === false && this.minAmount === false) {
        this.maxAmount = rates.limits.maximumAmountWithdrawal;
        this.minAmount = rates.limits.minimumAmountWithdrawal;
        this.buildForm();
      }
      this.blink();
    });

    this.dispatcherService.event$.pipe(
      filter(event => event instanceof ProductStatusChangedEvent),
      takeUntil(this.unsubscribe$)
    ).subscribe((event) => {
      this.clear();
      if (!event.payload.isActive) {
        this.state.customerProducts = this.state.customerProducts.filter(customerProduct => customerProduct.username !== event.payload.username);
      } else {
        this.state.customerProducts.push({
          username: event.payload.username,
          product: {
            name: productName(event.payload.product)
          }
        });
      }
    });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  clear(): void {
    this.state.form.reset();

    if (this.state.adjustedExchangeRates) {
      this.onAmountChange('amount');
    }
  }

  blink(): void {
    const element = document.getElementById('currentRate');
    if (element === null) {
      return;
    }

    // Check if form is initialized
    if (!this.state.form) {
      return;
    }

    of('').pipe(
      tap(() => element.classList.add('text-flash')),
      delay(3500),
      take(1)
    ).subscribe(() => {
      this.onAmountChange('usdcAmount');
      element.classList.remove('text-flash');
    });
  }

  onAmountChange(from: string): void {
    const isFromAmount = from === 'amount';
    const value = this.getSubTransactions().at(0).get(from).value || 0;
    const convertedAmountField = isFromAmount ? 'usdcAmount' : 'amount';
    const totalProp = isFromAmount ? 'totalAmount' : 'totalUsdcAmount';
    const totalConvertedProp =  isFromAmount ? 'totalUsdcAmount' : 'totalAmount';

    this.state[totalProp] = toFixed(value, isFromAmount ? EUR_DECIMAL_PLACES : USDT_DECIMAL_PLACES);

    if (!isNaN(value)) {
      // Check if adjustedExchangeRates exists
      if (!this.state.adjustedExchangeRates) {
        return;
      }

      const adjustedRate = this.getAdjustedRate(from, value);

      // Use convertToUsdc/convertFromUsdc for USDC as well since the utility functions are the same
      const conversion = isFromAmount ?
        this.utils.convertToUsdc(adjustedRate, value) :
        this.utils.convertFromUsdc(adjustedRate, value);

      this.state.adjustedUsdcValue = toFixed(this.getAdjustedRate(from, value), USDT_RATE_DECIMAL_PLACES);
      this.state[totalConvertedProp] = toFixed(conversion, isFromAmount ? USDT_DECIMAL_PLACES : EUR_DECIMAL_PLACES);
      this.updateConvertedField(convertedAmountField, conversion);
      this.updateFees();
    }
  }

  updateConvertedField(fieldName: string, value: string | number): void {
    const formArray = this.getSubTransactions();
    if (!formArray || formArray.length === 0) {
      return;
    }

    const control = formArray.at(0).get(fieldName);
    if (!control) {
      return;
    }

    control.setValue(value);
    control.updateValueAndValidity();
  }

  buildForm(): void {
    this.state.form = this.fb.group({
      email: this.fb.control(''),
      verificationCode: ['', [Validators.required, Validators.minLength(6), Validators.maxLength(6)]],
      usdcAddress: [this.formDetails.account_id, [Validators.required]],
      subTransactions: this.fb.array([this.createSubTransaction()])
    });
  }

  getSubTransactions(): FormArray {
    return <FormArray>this.state.form.get('subTransactions');
  }

  createSubTransaction(): FormGroup {
    return this.fb.group({
      amount: ['', [Validators.required]],
      usdcAmount: ['', [Validators.required, Validators.max(this.maxAmount), Validators.min(this.minAmount)]],
      username: ['', [Validators.required]],
      productCode: ['', [Validators.required]]
    });
  }

  getAdjustedRate(from: string, amount: number | string): number {
    // Check if adjustedExchangeRates exists
    if (!this.state.adjustedExchangeRates) {
      return 0;
    }

    const range = this.getRangeSetting(from, amount);
    return (range && range.adjustedRate) || (this.state.adjustedExchangeRates && this.state.adjustedExchangeRates.base_rate) || 0;
  }

  getRangeSetting(from: string, amount: number | string): any {
    // Check if adjustedExchangeRates and rate.withdrawal exist
    if (!this.state.adjustedExchangeRates || !this.state.adjustedExchangeRates.rate || !this.state.adjustedExchangeRates.rate.withdrawal) {
      return null;
    }

    const ranges = this.state.adjustedExchangeRates.rate.withdrawal;

    // Check if ranges is an array
    if (!Array.isArray(ranges) || ranges.length === 0) {
      return null;
    }

    let setRange = ranges.filter((range: any) => {
      return inBetween(amount, from === 'amount' ? range.amountFrom : range.from, from === 'amount' ? range.amountTo : range.to);
    }).pop();

    if(!setRange) {
      return ranges.slice(-1).pop();
    }

    return setRange;
  }

  updateFees(): void {
    this.state.fee = this.utils.feeDisplay(this.state.totalAmount, 'withdrawal');
    this.state.actualFee = this.utils.computeActualFee(this.state.totalAmount, 'withdrawal');
    this.state.companyFee = this.utils.computeActualFee(this.state.totalAmount, 'withdrawal', 'company');

    this.computeTotalEurAmount();
  }

  computeTotalEurAmount(): void {
    this.state.totalAmount = plus(this.state.totalAmount, this.state.actualFee);
  }

  onProductChange(_newValue: string, _oldValue: string, _idx: number, event: any): void {
    if (!event || !event.target) {
      return;
    }

    const selectedIndex = event.target.selectedIndex;
    if (selectedIndex < 0 || !event.target.options || !event.target.options[selectedIndex]) {
      return;
    }

    const selectElementText = event.target.options[selectedIndex].getAttribute('product-name');
    if (!selectElementText) {
      return;
    }

    this.updateConvertedField('productCode', this.utils.getProductCode(selectElementText));
  }

  getCode(): void {
    const payload = {
      signupType: 1,
      email: this.user.email,
      purpose: WithdrawUsdcComponent.OTP_PURPOSE
    };

    this.submitCode.emit(payload);
  }

  onSubmit(): void {
    this.reloadFormDetails(() => {
      const payload = this.createPayload();
      this.submit.emit(payload);
    }, true);
  }

  reloadFormDetails(_cb: Function, _showSpinner = false): void {
    this.userService.getActivePaymentOptions('Withdrawal', (res: any) => {
      for (let i = 0, l = res.length; i < l; i++) {
        let po = res[i];
        if (po.code.toLowerCase() === 'usdc') {
          this.formDetails['bundle'] = po.bundle;
          this.formDetails['fees'] = po.fees;
          break;
        }
      }

      _cb && _cb();
    }, _showSpinner);
  }

  createPayload() {
    const code = this.state.form.get('verificationCode').value;
    const payload = {
      totalEurAmount: this.state.totalAmount,
      accessToken: this.userService.getToken('api').access_token,
      usdc: {
        blockchainDetails: this.blockchainDetails,
        baseRate: this.state.adjustedExchangeRates.base_rate,
        adjustedRate: this.getAdjustedRate('amount', this.state.totalAmount),
        requestedUsdc: this.state.totalUsdcAmount
      },
      form: {
        customerFee: this.state.actualFee,
        companyFee: this.state.companyFee,
        gatewayId: this.formDetails.bundle.gateway.active && this.formDetails.bundle.gateway.id ? this.formDetails.bundle.gateway.id : '',
        usdcAddress: this.state.form.get('usdcAddress').value,
        paymentOptionType: WithdrawUsdcComponent.PAYMENT_OPTION,
        email: this.state.form.get('email').value !== '' ? this.state.form.get('email').value : this.user.email,

        verificationCode: String(code),
        subTransactions: {}
      },
      otpMethod: this.activeOTPMethod
    };

    let index = 0;
    const subTransactions = [];

    for (const subTransaction of this.getSubTransactions().value) {
      const customerProduct = this.state.customerProducts.filter((cp: any) => cp.username === subTransaction.username)
        .pop();

      subTransactions[index++] = {
        username: customerProduct.username,
        product_code: customerProduct.product.code,
        amount: subTransaction.amount
      };
    }

    payload.form.subTransactions = subTransactions;

    return payload;
  }

  switchOTPMethod(): void {
    this.changeOTPMethod();

    const verificationCodeControl = this.state.form.get('verificationCode');
    if (verificationCodeControl) {
      verificationCodeControl.reset();
    }
  }
}
