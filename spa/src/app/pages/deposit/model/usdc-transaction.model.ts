import Decimal from 'decimal.js';
import * as moment from 'moment';
import {USDT_RATE_DECIMAL_PLACES, USDT_AMOUNT_DECIMAL_PLACES, EUR_DECIMAL_PLACES, USDT_DECIMAL_PLACES} from '../../../shared/utils/number.utils';
import { BLOCKCHAIN_CONFIRMATION_STATUS } from '../../../shared/model/transaction.model';

export class UsdcTransaction {
  id: number;
  amount: Decimal;
  receivingAddress: string;
  senderAddress: string;
  usdcWallet: string;
  confirmedThreshold: number
  confirmationCount?: number;
  confirmationStatus?: string
  rate: string;
  isRateExpired: boolean;
  isDeclined: boolean;
  isVoided: boolean;
  isAcknowledged: boolean;
  lockDownTimeRemaining: moment.Moment;
  eurAmount: string;
  usdcAmount: string;
  transactionHash: string;
  seenPendingConfirmation: boolean = false;
  pendingAt: string;
  productName: string;
  requestedNetEurAmount: string;
  requestedFeeAmount: string;

  constructor(response: any) {
    this.id = +response.id;
    this.confirmedThreshold = response.details.usdc.confirmed_threshold || 12;
    this.confirmationCount = isNaN(response.details.usdc.confirmation_count) ?
      0 : response.details.usdc.confirmation_count > this.confirmedThreshold ? this.confirmedThreshold : response.details.usdc.confirmation_count;
    const rate = parseFloat(response.details.usdc.rate);
    this.rate = rate.countDecimals() >= EUR_DECIMAL_PLACES ? rate.toPrecision() : rate.toFixed(EUR_DECIMAL_PLACES);

    this.eurAmount = response.amount.toFixed(EUR_DECIMAL_PLACES);
    this.usdcAmount = parseFloat(response.details.usdc.requested_amount).toFixed(USDT_DECIMAL_PLACES);
    this.usdcWallet = response.details.usdc.customer_wallet || 'None selected';
    this.receivingAddress = response.details.usdc.receiver_unique_address;
    this.senderAddress = response.details.usdc.sender_address;
    const parts = response.lock_down_rate_time_remaining.split(':');
    this.lockDownTimeRemaining = moment(parts, 'HH:mm:ss');
    this.isDeclined = response.status.id === 3;
    this.isAcknowledged = response.status.id === 4;
    this.confirmationStatus = response.details.usdc.confirmation_status;
    this.isVoided = response.is_voided;
    this.transactionHash = response.details.reference_number || '';
    this.seenPendingConfirmation = response.details.usdc.seen_pending_notification !== undefined ?
      response.details.usdc.seen_pending_notification : false;

    this.pendingAt = response.details.usdc.pending_at !== undefined ?
      response.details.usdc.pending_at : null;

    this.productName = response.product.name || '';
    this.requestedNetEurAmount = parseFloat(response.details.summary.customer_amount).toFixed(EUR_DECIMAL_PLACES);
    this.requestedFeeAmount = parseFloat(response.details.summary.customer_fee).toFixed(EUR_DECIMAL_PLACES);
  }

  get isRequested(): boolean {
    if (this.isDeclined) {
      return false;
    }
    if (this.isFailed && !this.isPending) {
      return true;
    }

    return !this.isConfirmed && !this.isPending && !this.isAcknowledged;
  }

  get isFailed(): boolean {
    return this.confirmationStatus === BLOCKCHAIN_CONFIRMATION_STATUS.Failed;
  }

  get isConfirmed(): boolean {
      return this.confirmationStatus === BLOCKCHAIN_CONFIRMATION_STATUS.Success;
  }

  get isPending(): boolean {
    return (!!this.pendingAt && !this.isConfirmed) || this.confirmationStatus === BLOCKCHAIN_CONFIRMATION_STATUS.PendingConfirmation;
  }

  /**
   * There would be instance that the lock down rate time is already
   * expired (00:00:00) but the rate_expired property of the transaction
   * is still 'false.'
   */
  get shouldBeExpired(): boolean {
    if (this.isPending) {
      return false;
    }

    return !this.isRateExpired && this.isLockDownRateTimeExpired;
  }

  get isLockDownRateTimeExpired(): boolean {
    return this.lockDownTimeRemaining.format('HH:mm:ss') === '00:00:00';
  }
}
