import { Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import {UsdcTransaction} from "../../model/usdc-transaction.model";
import {MwaApiService} from "../../../../shared/services/mwa-api.service";
import {UserService} from "../../../../shared/services/user.service";
import {AlertService} from "../../../../shared/ui/alert.service";
import {TranslateService} from "@ngx-translate/core";
import {FormArray, FormBuilder, FormGroup, Validators} from "@angular/forms";
import {of, Subject, timer} from "rxjs";
import {delay, filter, mergeMap, take, takeUntil, tap} from "rxjs/operators";
import { inBetween, sub, toFixed } from "../../../../shared/utils/decimal.util";
import {EUR_DECIMAL_PLACES, USDT_RATE_DECIMAL_PLACES, USDT_DECIMAL_PLACES} from "../../../../shared/utils/number.utils";
import transactionUtil from "../../../../shared/utils/transaction.util";
import {ClipboardService} from "ngx-clipboard";
import {WebsocketTopics} from "../../../../shared/ws/topics";
import {ProductStatusChangedEvent} from "../../../../shared/event/product-status-changed.event";
import {productName} from "../../../../shared/utils/products.util";
import {TransactionProcessedEvent} from "../../../../shared/event/transaction-processed-event";
import {WebsocketService} from "../../../../shared/ws/websocket.service";
import {EventDispatcherService} from "../../../../shared/event/event-dispatcher.service";
import { NoWhiteSpaceValidator } from "../../../../shared/validators/no-whitespace.validator";
import { TransactionConfirmationEvent } from '../../../../shared/event/transaction-confirmation-event';
import { BLOCKCHAIN_CONFIRMATION_STATUS } from '../../../../shared/model/transaction.model';
import { ConfigService } from 'src/app/shared/services/config.service';

declare let window: any;

@Component({
  selector: 'app-deposit-usdc',
  templateUrl: './deposit-usdc.component.html',
  styleUrls: ['./deposit-usdc.component.css']
})

export class DepositUsdcComponent implements OnInit, OnDestroy {
  static PAYMENT_OPTION = 'USDC';
  @Input() user: any;
  @Input() formDetails: any;
  @Input() isSubmitting: boolean;
  @Output() submit: EventEmitter<any> = new EventEmitter<any>();
  @Output() usdcDeclined: EventEmitter<any> = new EventEmitter<any>();

  session: any = {};
  qrc = '';
  accessToken: string;
  usdcTransaction: UsdcTransaction;
  transactionHash = '';
  rateInterval: any = 60000;
  state: any = {};
  data: any = {};
  lastUsdc: object = null;
  utils: any;
  minAmount: any = 0;
  maxAmount: any = 0;
  email_payment: string;
  unsubscribe$ = new Subject<any>();
  showOtherWalletField: boolean = false;
  customerProducts: any = {};
  blockchainDetails: any = {
    fixedAdjustment: '',
    adjustmentType: '',
    rates: ''
  };
  rateTimeoutId: any = null;
  isWaitingModalShown: boolean = false;
  isRateTimerRunning: boolean = false;

  constructor(
    public configService: ConfigService,
    private mwaApiService: MwaApiService,
    private userService: UserService,
    private dispatcherService: EventDispatcherService,
    private alertService: AlertService,
    private translate: TranslateService,
    private _elementRef: ElementRef,
    private fb: FormBuilder,
    private _copyService: ClipboardService,
    private websocketService: WebsocketService,
  ) {
    this.accessToken = this.userService.getToken('api').access_token;
    this.data['usdc'] =  {
      paymentsEmail: this.email_payment,
      signupType: 0,
      transactionStatus: 'request',
      paymentOptionType: DepositUsdcComponent.PAYMENT_OPTION,
      fee: 0,
      confirmSteps: 10,
      feeAmount: 'Free',
      product: 'PINBET',
      step: 0,
      deposit: {
        eurAmount: '',
        usdcAmount: '',
        address: '',
        confirmCount: '',
        rate: ''
      },
      countdownFormat: '00:00:00',
    };
  }

  ngOnInit() {
    this.session = JSON.parse(localStorage.getItem('session'));
    this.data['usdc']['email'] = this.session['email'];
    this.state = {
      adjustedExchangeRates: null,
      adjustedUsdcValue: null,
      currency: this.session.currency.code,
      customerProducts: this.session.products.filter((customerProduct) => customerProduct.is_active),
      totalAmount: '0.00',
      totalUsdcAmount: '0.00',
      fee: '',
      actualFee: '0.00',
      companyFee: '0.00',
      requestForm: null,
      usdcWallet: '',
      usdcSenderAddress: '',
    };

    this.utils = transactionUtil(this.formDetails.bundle, this.formDetails.fees);
    this.updateFees();

    timer(1000, 1000).pipe(
      takeUntil(this.unsubscribe$),
      filter(seconds => seconds % 60 === 0),
      mergeMap(() => this.mwaApiService.getRates(DepositUsdcComponent.PAYMENT_OPTION, this.session.currency.code))
    ).subscribe((rates) => {
      this.state.adjustedExchangeRates = rates;
      this.state.adjustedUsdcValue = toFixed(
        this.getAdjustedRate('usdcAmount', this.state.totalAmount),
        USDT_RATE_DECIMAL_PLACES
      );

      this.maxAmount = rates.limits.maximumAmountDeposit;
      this.minAmount = rates.limits.minimumAmountDeposit;
      this.buildForm();
      this.blink();
    });

    this.getLastTransactionUsdc();
    this.listenToEvents();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  createPayload() {
    const payload = {
      accessToken: this.accessToken,
      usdc: {
        blockchainDetails: this.blockchainDetails,
        baseRate: this.state.adjustedExchangeRates.base_rate,
        adjustedRate: this.state.adjustedUsdcValue,
        requestedUsdc: this.state.totalUsdcAmount,
        senderAddress: this.getSubTransactions().at(0).get('senderAddress').value.trim(),
        customerWallet: this.getSubTransactions().at(0).get('usdcWallet').value == 'Others'
          ? this.getSubTransactions().at(0).get('othersWalletName').value.trim()
          : this.getSubTransactions().at(0).get('usdcWallet').value,
      },
      form: {
        email: this.state.form.get('email').value !== '' ? this.state.form.get('email').value : this.session.email,
        paymentOptionType: DepositUsdcComponent.PAYMENT_OPTION,
        customerFee: this.state.actualFee,
        companyFee: this.state.companyFee,
        gatewayId: this.formDetails.bundle.gateway.active && this.formDetails.bundle.gateway.id ? this.formDetails.bundle.gateway.id : '',
        subTransactions: {}
      }
    };

    let index = 0;
    const subTransactions = [];
    for (const subTransaction of this.getSubTransactions().value) {
      const customerProduct = this.state.customerProducts.filter((cp) => cp.username === subTransaction.username)
        .pop();

      subTransactions[index++] = {
        username: customerProduct.username,
        product_code: customerProduct.product.code,
        amount: subTransaction.amount
      };
    }

    payload.form.subTransactions = subTransactions;

    return payload;
  }

  saveTransactionHash() {
    this.updateBlockChainTransactionHash(this.state.requestForm.get('transactionHash').value)
  }

  updateBlockChainTransactionHash(transactionHash) {
    this.mwaApiService.updateBlockChainTransactionHash({
        access_token: this.accessToken,
        type: 1,
        transactionHash: transactionHash,
        paymentOptionType: DepositUsdcComponent.PAYMENT_OPTION
      },
      res => {
        if (res.data) {
          this.alertService.success({
            module: 'deposit',
            title: this.translate.instant('alike.success'),
            message: this.translate.instant('Transaction hash has been saved successfully.'),
            subMessage: '',
          });
          this.getLastTransactionUsdc();
        }
      }
    );
  }

  showQRCodeDialog(id) {
    const qrcodeWrap = this._elementRef.nativeElement.querySelector('#usdc-qrcode-wrap');
    const usdcQRCode = document.getElementById('usdc-qr-code');
    const dialog = document.getElementById(id);

    qrcodeWrap.innerHTML = usdcQRCode.innerHTML;

    dialog && dialog['show']();
  }

  getLastTransactionUsdc(expanded = false) {
    this.mwaApiService.getLastTransactionBlockChain({
        access_token: this.accessToken,
        type: 'deposit',
        paymentOptionType: DepositUsdcComponent.PAYMENT_OPTION
      },
      res => {
        if (res.data) {
          if (res.data.status.id !== 4) {
            if (expanded) {
              setTimeout(() => {
                window.expandableElement('usdc-pending');
              }, 500);
            }

            this.usdcTransaction = new UsdcTransaction(res.data);

            this.qrc = this.usdcTransaction.receivingAddress;
            this.transactionHash = res.data.details.reference_number;

            if (this.usdcTransaction.isRequested || this.usdcTransaction.isPending || this.usdcTransaction.isConfirmed) {
              if (!this.isRateTimerRunning) {
                this.startRateCountdown();
                this.isRateTimerRunning = true;
              }
              this.buildRequestForm();
            }

            if (this.usdcTransaction.isPending && !this.usdcTransaction.seenPendingConfirmation) {
              this.alertService.success({
                module: 'deposit',
                title: this.translate.instant('alike.pending_confirmation_allcaps'),
                message: this.translate.instant(
                  'transaction.btc_pending_confirmation',
                  { paymentOption: DepositUsdcComponent.PAYMENT_OPTION}
                ),
              },  () => {
                this.seenPendingConfirmationModal();
              });
            }
          } else {
            this.resetForm();
          }
        } else {
          this.resetForm();
        }
      }
    );
  }

  onUsdcContainerClicked(): void {
    if ((this.usdcTransaction.isRateExpired || this.usdcTransaction.isLockDownRateTimeExpired) && (!this.usdcTransaction.isPending && !this.usdcTransaction.isConfirmed)) {
      this.showWaitingModal()
    }
  }

  showWaitingModal() {
    this.alertService.info({
      title: 'Waiting',
      module: 'deposit',
      message: this.translate.instant('transaction.send_usdt_submessage', { paymentOption: DepositUsdcComponent.PAYMENT_OPTION }),
      dismissMessage: this.translate.instant('action.dismiss'),
    })
  }

  startRateCountdown(): void {
    if (this.usdcTransaction.isRateExpired || this.usdcTransaction.isLockDownRateTimeExpired) {
      if (this.rateTimeoutId) {
        window.clearTimeout(this.rateTimeoutId);
      }
      return;
    }

    this.rateTimeoutId = window.setTimeout(() => {
      const timerDom = document.getElementsByClassName('running-usdc-rate-timer')[0];

      const timeRemaining = this.usdcTransaction.lockDownTimeRemaining;
      const isExpired = (moment) => {
        return moment.format('HH:mm:ss') === '00:00:00';
      };

      timerDom.innerHTML = timeRemaining.format('HH:mm:ss');

      this.rateInterval = window.setInterval(() => {
        timeRemaining.subtract(1, 'seconds');

        if (
          isExpired(timeRemaining)
          && (!this.usdcTransaction.isPending && !this.usdcTransaction.isConfirmed)
          && !this.isWaitingModalShown
        ) {
          this.usdcTransaction.isRateExpired = true;
          this.isWaitingModalShown = true;

          if (this.isDepositPage()) {
            this.showWaitingModal();
          }

          window.clearInterval(this.rateInterval);
          if (this.rateTimeoutId) {
            window.clearTimeout(this.rateTimeoutId);
          }
        } else {
          timerDom.innerHTML = timeRemaining.format('HH:mm:ss');
        }
      }, 1000);
    }, 100);
  }

  buildRequestForm(): void {
    this.state.requestForm = this.fb.group({
      transactionHash: this.fb.control(this.transactionHash, [Validators.required, Validators.pattern(/[\S]/g)]),
    });
  }

  checkTransactionHash() {
    const transactionHashValue = this.state.requestForm.get('transactionHash').value;

    if (transactionHashValue !== null) {
      this.state.requestForm.get('transactionHash').disable();
    } else {
      this.state.requestForm.get('transactionHash').enable();
    }
  }

  seenPendingConfirmationModal(): void {
    this.mwaApiService.seenBlockChainPendingTransaction({
      access_token: this.accessToken,
      paymentOptionType: DepositUsdcComponent.PAYMENT_OPTION
    }).subscribe(() => {
      return;
    })
  }

  resetForm(): void {
    this.data['usdc'].step = 1;
    this.lastUsdc = null;
    this.usdcTransaction = null;
    this.clear();
    this.buildForm();
  }

  clear() {
    const subTransactions = <FormArray> this.getSubTransactions();
    const usdcCustomerProductField = (<HTMLSelectElement>document.getElementById('usdc-customer-product'));
    const usdcWalletField = (<HTMLSelectElement>document.getElementById('usdc-wallet'));

    if (Object.keys(subTransactions).length) {
      subTransactions.removeAt(1);
      subTransactions.reset();
    }

    // set select input to selected-disabled value
    usdcCustomerProductField ? usdcCustomerProductField.selectedIndex = 0 : null;
    usdcWalletField ? usdcWalletField.selectedIndex = 0 : null;

    if (this.state.adjustedExchangeRates) {
      this.onAmountChange('amount');
    }
  }

  buildForm(): void {
    this.state.form = this.fb.group({
      email: this.fb.control(''),
      subTransactions: this.fb.array([this.createSubTransaction()])
    });
  }

  createSubTransaction(): FormGroup {
    const {defaultProduct, productCode} = this.utils.getDefaultProduct(this.state.customerProducts, this.user.loginPath);
    let usdcCustomerProduct = defaultProduct;
    let usdcSenderAddress = this.formDetails.hasOwnProperty('senderAddress') ? this.formDetails.senderAddress : '';
    let usdcWallet = '';
    let usdcOtherWallet = '';

    let usdcAmount = toFixed(0, USDT_DECIMAL_PLACES)
    const amount = toFixed(0, EUR_DECIMAL_PLACES);

    const usdcCustomerProductField = (<HTMLSelectElement>document.getElementById('usdc-customer-product'))
    if (usdcCustomerProductField) {
      usdcCustomerProduct = usdcCustomerProductField.selectedOptions[0].value;
    }

    const usdcAmountField = (<HTMLInputElement>document.getElementById('usdc-input-field'))
    if (usdcAmountField && usdcAmountField.value) {
      usdcAmount = usdcAmountField.value;
    }

    const usdcOtherWalletField = (<HTMLInputElement>document.getElementById('usdc-other-wallet'))
    if (usdcOtherWalletField) {
      usdcOtherWallet = usdcOtherWalletField.value;
    }

    const usdcSenderAddressField = (<HTMLInputElement>document.getElementById('usdc-sender-address'))
    if (usdcSenderAddressField && usdcSenderAddressField.value) {
      usdcSenderAddress = usdcSenderAddressField.value;
    }

    const usdcWalletField = (<HTMLSelectElement>document.getElementById('usdc-wallet'))
    if (usdcWalletField) {
      usdcWallet = usdcWalletField.selectedOptions[0].value;
    }

    const senderAddressValidator = [NoWhiteSpaceValidator()];
    if (this.formDetails.fields && this.formDetails.fields['sender_address']['required']) {
      senderAddressValidator.push(Validators.required);
    }

    return this.fb.group({
      amount: [amount, [Validators.required]],
      usdcWallet: [usdcWallet],
      senderAddress: [usdcSenderAddress, senderAddressValidator],
      usdcAmount: [usdcAmount, [Validators.required, Validators.max(this.maxAmount), Validators.min(this.minAmount)]],
      othersWalletName: [usdcOtherWallet, [NoWhiteSpaceValidator()]],
      username: [usdcCustomerProduct, [Validators.required]],
      productCode: [productCode, [Validators.required]],
    });
  }

  getAdjustedRate(from, amount) {
    const range = this.getRangeSetting(from, amount);
    return (range && range.adjustedRate) || this.state.adjustedExchangeRates.base_rate;
  }

  getRangeSetting(from, amount) {
    const ranges = this.state.adjustedExchangeRates.rate.deposit;
    let setRange = ranges.filter((range)=>{
      return inBetween(
        amount,
        from === 'amount' ? range.amountFrom : range.from,
        from === 'amount' ? range.amountTo : range.to
      );
    }).pop();

    if(!setRange) {
      return ranges.slice(-1).pop();
    }

    return setRange;
  }

  blink(): void {
    const element = document.getElementById('currentRate');
    if (element === null) {
      return;
    }

    this.onAmountChange('usdcAmount');

    of('').pipe(
      tap(() => element.classList.add('text-flash')),
      delay(3500),
      take(1)
    ).subscribe(() => {

      element.classList.remove('text-flash');
    });
  }

  onAmountChange(from): void {
    const isFromAmount = from === 'amount';
    const value = this.getSubTransactions().at(0).get(from).value || 0;
    const convertedAmountField = isFromAmount ? 'usdcAmount' : 'amount';
    const totalProp = isFromAmount ? 'totalAmount' : 'totalUsdcAmount';
    const totalConvertedProp =  isFromAmount ? 'totalUsdcAmount' : 'totalAmount';

    this.state[totalProp] = toFixed(value, isFromAmount ? EUR_DECIMAL_PLACES : USDT_DECIMAL_PLACES);
    if (!isNaN(value)) {
      const adjustedRate = this.getAdjustedRate(from, value);
      const conversion = isFromAmount ? this.utils.convertToBtc(adjustedRate, value) : this.utils.convertFromBtc(adjustedRate, value);
      this.state.adjustedUsdcValue = toFixed(this.getAdjustedRate(from, value), USDT_RATE_DECIMAL_PLACES);
      this.state[totalConvertedProp] = toFixed(conversion, isFromAmount ? USDT_DECIMAL_PLACES : EUR_DECIMAL_PLACES);
      this.updateConvertedField(convertedAmountField, conversion);
      this.updateFees();
    }
  }

  getSubTransactions(): FormArray {
    if (this.state.form) {
      return <FormArray>this.state.form.get('subTransactions');
    }
    return <FormArray>{};
  }

  onProductChange(newValue, oldValue, idx: number, event): void {
    const selectedIndex = event.target.selectedIndex;
    const selectElementText = event.target.options[selectedIndex].getAttribute('product-name');
    this.updateConvertedField('productCode', this.utils.getProductCode(selectElementText));
  }

  updateConvertedField(fieldName, value): void {
    const control = this.getSubTransactions().at(0).get(fieldName);
    control.setValue(value);
    control.updateValueAndValidity();
  }

  updateFees(): void {
    this.state.fee = this.utils.feeDisplay(this.state.totalAmount, 'deposit');
    this.state.actualFee = this.utils.computeActualFee(this.state.totalAmount, 'deposit');
    this.state.companyFee = this.utils.computeActualFee(this.state.totalAmount, 'deposit', 'company');

    this.computeTotalEurAmount();
  }

  computeTotalEurAmount(): void {
    this.state.totalAmount = sub(this.state.totalAmount, this.state.actualFee);
  }

  onProductWalletChange(newValue): void {
    this.showOtherWalletField = newValue === "Others";
  }

  onSubmit() {
    this.reloadFormDetails(() => {
      const payload = this.createPayload();
      this.submit.emit(payload);
    }, true);
  }

  reloadFormDetails(_cb, _showSpinner = false): void {
    this.userService.getActivePaymentOptions('deposit', (res) => {
      for (let i = 0, l = res.length; i < l; i++) {
        let po = res[i];
        if (po.code.toLowerCase() === 'usdc') {
          this.formDetails['bundle'] = po.bundle;
          this.formDetails['fees'] = po.fees;
          this.formDetails['fields'] = po.fields.pop();
          break;
        }
      }

      _cb && _cb();
    }, _showSpinner);
  }

  copyReceivingAddress() {
    const address = <HTMLInputElement>document.getElementById('usdc-recipient-address');
    this._copyService.copyFromContent(address.value);
    document.getElementById('copyUsdcAddressBtn').innerText = this.translate.instant('action.copied');
    setTimeout( () => {
      document.getElementById('copyUsdcAddressBtn').innerText = this.translate.instant('action.copy');
    }, 5000);
  }

  isDepositPage() {
    return window.location && window.location.pathname.includes('deposit');
  }

  listenToEvents(): void {
    // Handle WebSocket subscription
    this.websocketService.addSubscription(
      `${WebsocketTopics.TRC20_REQUEST_STATUS}.${this.session.channel}`,
      (args) => {
        this.getLastTransactionUsdc();
      }
    );

    // Handle Dispatcher events
    this.dispatcherService.event$.pipe(
      filter(event =>
        event instanceof ProductStatusChangedEvent ||
        event instanceof TransactionProcessedEvent ||
        event instanceof TransactionConfirmationEvent
      ),
      takeUntil(this.unsubscribe$)
    ).subscribe((event) => {
      if (event instanceof ProductStatusChangedEvent) {
        this.handleProductStatusChanged(event);
      } else if (event instanceof TransactionProcessedEvent) {
        this.handleTransactionProcessed(event);
      } else if (event instanceof TransactionConfirmationEvent) {
        this.handleTransactionConfirmation(event);
      }
    });
  }

  handleProductStatusChanged(event: ProductStatusChangedEvent) {
    this.clear();
    if (!event.payload.isActive) {
      this.state.customerProducts = this.state.customerProducts.filter(customerProduct => customerProduct.username !== event.payload.username);
    } else {
      this.state.customerProducts.push({
        username: event.payload.username,
        product: {
          name: productName(event.payload.product)
        }
      });
    }
  }

  handleTransactionProcessed(event: TransactionProcessedEvent) {
    const eventPayload = event.payload;
    if (
      eventPayload['status'] === 'Declined'
      && eventPayload['type'] === 'deposit'
      && eventPayload['payment_option'] === DepositUsdcComponent.PAYMENT_OPTION
      && this.isDepositPage()
    ) {
      this.usdcDeclined.emit();
      this.alertService.danger({
        module: 'deposit',
        title: this.translate.instant('transaction.request_declined'),
        message: eventPayload['reason'] ? this.translate.instant(eventPayload['reason']) : this.translate.instant('transaction.blockchain_auto_declined_message', { paymentOption: 'USDC' }),
        subMessage: '',
      }, () => {
        this.resetForm();
        this.getLastTransactionUsdc();
      });
    }

    if (eventPayload['status'] === 'Acknowledged'
      && eventPayload['type'] === 'deposit'
      && eventPayload['payment_option'] === DepositUsdcComponent.PAYMENT_OPTION
    ) {
      setTimeout(() => {
        location.reload();
      }, 2000);
    }
  }

  handleTransactionConfirmation(event: TransactionConfirmationEvent) {
    const { confirmationStatus, type, payment_option } = event.payload;

    if (confirmationStatus === BLOCKCHAIN_CONFIRMATION_STATUS.PendingConfirmation
      && type === 'Deposit'
      && payment_option === DepositUsdcComponent.PAYMENT_OPTION
      && (this.usdcTransaction && !this.usdcTransaction.seenPendingConfirmation)
    ) {
      this.usdcTransaction.confirmationStatus = confirmationStatus;

      this.alertService.success({
        module: 'deposit',
        title: this.translate.instant('alike.pending_confirmation_allcaps'),
        message: this.translate.instant(
          'transaction.btc_pending_confirmation',
          { paymentOption: DepositUsdcComponent.PAYMENT_OPTION }
        ),
      }, () => {
        this.seenPendingConfirmationModal();
      });
    }

    if (confirmationStatus === BLOCKCHAIN_CONFIRMATION_STATUS.Success
      && type === 'Deposit'
      && payment_option === DepositUsdcComponent.PAYMENT_OPTION
      && this.usdcTransaction
    ) {
      this.usdcTransaction.confirmationStatus = confirmationStatus;

      this.alertService.success({
        module: 'deposit',
        title: this.translate.instant('alike.confirmed'),
        message: this.translate.instant('transaction.btc_confirmed_new'),
        subMessage: this.translate.instant('transaction.btc_process'),
      }, () => {
        this.getLastTransactionUsdc();
      });
    }
  }
}
