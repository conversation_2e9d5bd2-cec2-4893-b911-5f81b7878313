import { inBetween } from "../../shared/utils/decimal.util";
import { BITCOIN_DECIMAL_PLACES, USDT_DECIMAL_PLACES, EUR_DECIMAL_PLACES, Decimal } from "./number.utils";

export default function transactionUtil(bundle, fees) {
  const convertFromBtc = (adjustedRate: Decimal, amount) => {
    const converted = new Decimal(amount);
    return converted.mul(adjustedRate).toFixed(EUR_DECIMAL_PLACES);
  };

  const convertToBtc = (adjustedRate: Decimal, amount) => {
    const converted = new Decimal(amount);
    return converted.div(adjustedRate).toFixed(BITCOIN_DECIMAL_PLACES);
  };

  const convertFromTrc20 = (adjustedRate: Decimal, amount) => {
    const converted = new Decimal(amount);
    return converted.mul(adjustedRate).toFixed(EUR_DECIMAL_PLACES);
  };

  const convertToTrc20 = (adjustedRate: Decimal, amount) => {
    const converted = new Decimal(amount);
    return converted.div(adjustedRate).toFixed(USDT_DECIMAL_PLACES);
  };

  const convertFromErc20 = (adjustedRate: Decimal, amount) => {
    const converted = new Decimal(amount);
    return converted.mul(adjustedRate).toFixed(EUR_DECIMAL_PLACES);
  };

  const convertToErc20 = (adjustedRate: Decimal, amount) => {
    const converted = new Decimal(amount);
    return converted.div(adjustedRate).toFixed(USDT_DECIMAL_PLACES);
  };

  const convertFromUsdc = (adjustedRate: Decimal, amount) => {
    const converted = new Decimal(amount);
    return converted.mul(adjustedRate).toFixed(EUR_DECIMAL_PLACES);
  };

  const convertToUsdc = (adjustedRate: Decimal, amount) => {
    const converted = new Decimal(amount);
    return converted.div(adjustedRate).toFixed(USDT_DECIMAL_PLACES);
  };

  const bundleFieldValue = (fieldName: string) => {
    if (bundle.hasOwnProperty('data')) {
      if (bundle.data.length) {
        const field = bundle.data.filter(f => f.name === fieldName).pop();
        const actualFeeDisplay = field ? field.value : '';

        return actualFeeDisplay == 0 ? 'FREE' : actualFeeDisplay;
      }
    }

    return '';
  };

  const bundleFieldLabel = (fieldName: string) => {
    if (bundle.hasOwnProperty('data')) {
      if (bundle.data.length) {
        const field = bundle.data.filter(f => f.name === fieldName).pop();
        return field ? field.label : '';
      }
    }

    return '';
  };

  const computeActualFee = (amount, transactionType: string, feeType = 'member') => {
    let actualFee = '0.00';
    const setting = feeSetting(amount, transactionType, feeType);

    if (hasEnabledFee(transactionType) && setting.length && setting.length > 0) {
      const customFeeType = customFeesType(transactionType);
      const fixedValue = (setting[0]).hasOwnProperty('fixed') ? (setting[0].fixed).valueOf() : '0.00';
      const customFee = customFeeType == 'percentage' ? amount * ((setting[0].percentage).valueOf() / 100) : fixedValue;

      actualFee = customFee.toString();
    } else if (feeType === 'member') {
      const gatewayFee = gatewayFeeSetting(transactionType, 'member');
      if (gatewayFee && hasActiveGateway()) {
        const gatewayFeeType = gatewayFeesType(transactionType);
        const computedGatewayFee = gatewayFeeType == 'percentage' ? amount * (gatewayFee.valueOf() / 100) : gatewayFee.valueOf();

        actualFee = computedGatewayFee.toString();
      } else {
        actualFee = bundleFieldValue(`${transactionType}_fee`).toLowerCase() === 'free' ? '0.00' : bundleFieldValue(`${transactionType}_fee`);

        if (actualFee.includes('%')) {
          const percentage = parseFloat(actualFee.slice(0, -1));
          const bundleFee = new Decimal(amount).mul(new Decimal(percentage.valueOf()).div(new Decimal(100)));

          actualFee = bundleFee.toString();
        }
      }
    } else if (feeType === 'company') {
      const companyGatewayFee = gatewayFeeSetting(transactionType, 'company');
      if (companyGatewayFee && hasActiveGateway()) {
        const companyGatewayFeeType = gatewayFeesType(transactionType);
        const computedCompanyGatewayFee = companyGatewayFeeType == 'percentage' ? amount * (companyGatewayFee.valueOf() / 100) : companyGatewayFee.valueOf();

        actualFee = computedCompanyGatewayFee.toString();
      }
    }

    return roundDownUp(actualFee, 2);
  };

  const roundDownUp = (number, decimals) => {
      const x = Math.pow(10, Number(decimals) + 2);
      return (Number(number) + (1 / x)).toFixed(decimals);
  }

  const feeDisplay = (amount, transactionType: string) => {
    if (hasEnabledFee(transactionType)) {
      const setting = feeSetting(amount, transactionType);
      if (setting.length) {
        const customFeeType = customFeesType(transactionType);
        if (customFeeType == 'percentage') {
          return setting[0].percentage == 0 ? 'FREE' : `${setting[0].percentage} %`;
        } else {
          return setting[0].fixed == 0 ? 'FREE' : `${setting[0].fixed} EUR`;
        }
      }
    } else {
      const gatewayFee = gatewayFeeSetting(transactionType, 'member');
      if (gatewayFee && hasActiveGateway()) {
        const gatewayFeeType = gatewayFeesType(transactionType);
        if (gatewayFeeType == 'percentage') {
          return gatewayFee == 0 ? 'FREE' : `${gatewayFee} %`;
        } else {
          return gatewayFee == 0 ? 'FREE' : `${gatewayFee} EUR`;
        }
      }
    }

    return bundleFieldValue(transactionType + '_fee');
  };

  const feeSetting = (amount, transactionType: string, feeType: string = 'member') => {
    const _feeSetting = fees[transactionType][feeType];
    if (_feeSetting !== undefined) {
      return _feeSetting.filter((setting) => {
        if (setting.from !== null) {
          return inBetween(amount, setting.from, setting.to);
        }
      });
    }

    return [];
  };

  const gatewayFeeSetting = (transactionType: string, feeType: string) => {
    const type = transactionType == 'withdrawal' ? 'withdraw' : transactionType;
    if (hasGatewayFee(type)) {
      const gatewayFeeSetting = bundle.gateway.fees[type][type];
        if (gatewayFeeSetting !== undefined) {
          return feeType === 'member' ? gatewayFeeSetting.memberFeePercentage : gatewayFeeSetting.companyFeePercentage;
        }
    }

    return 0;
  }

  const gatewayFeesType = (transactionType: string) => {
    const type = transactionType == 'withdrawal' ? 'withdraw' : transactionType;
    if (hasGatewayFee(type)) {
      return bundle.gateway.fees[type][type]['feesType'];
    }

    return 'percentage';
  }

  const hasGatewayFee = (type: string) => {
    if (bundle.gateway) {
      if (bundle.gateway.hasOwnProperty('fees')) {
        return bundle.gateway.fees.hasOwnProperty(type);
      }
    }

    return false;
  }

  const hasActiveGateway = () => {
    if (bundle.gateway) {
      return bundle.gateway.id && bundle.gateway.active == 1;
    }

    return false;
  }

  const hasEnabledFee = (transactionType: string) => {
    return fees.enabled && fees[transactionType].member.length !== 0;
  };

  const customFeesType = (transactionType: string) => {
    if (hasEnabledFee(transactionType)) {
      let fee = fees[transactionType];
      if (fee.hasOwnProperty('feesType')) {
        return fee.feesType;
      }
    }

    return 'percentage';
  }

  const getProductCode = (productName): string => {
    switch (productName) {
      case 'Sports':
        return 'PINBET';
      case 'Member Wallet':
        return 'PWM';
      case 'Casino':
        return 'EVOLUTION';
      case 'PIWIXchange':
        return 'PIWIX';
      case 'PIWI Slot':
      case 'PIWI Slots':
      case 'WHITECLIFF':
        return 'WHITECLIFF';
      default:
        return '';
    }
  };

  const getDefaultProduct = (customerProducts, loginPath) => {
    let defaultProduct = '';
    let productCode = '';

    // If customerProducts is valid, proceed with normal logic
    switch (loginPath) {
      case 'sports':
        defaultProduct = getProductUsername(customerProducts, 'Sports');
        productCode = getProductCode('Sports');
        break;
      case 'casino':
        defaultProduct = getProductUsername(customerProducts, 'Casino');
        productCode = getProductCode('Casino');
        break;
      case 'whitecliff':
        defaultProduct = getProductUsername(customerProducts, 'PIWI Slots');
        productCode = getProductCode('PIWI Slots');
        break;
      case 'home':
        defaultProduct = getProductUsername(customerProducts, 'Member Wallet');
        productCode = getProductCode('Member Wallet');
        break;
      case 'exchange':
        defaultProduct = getProductUsername(customerProducts, 'PIWIXchange');
        productCode = getProductCode('PIWIXchange');
        break;
    }

    console.log('loginPath', loginPath);
    console.log('defaultProduct', defaultProduct);
    console.log('productCode', productCode);

    return {defaultProduct, productCode};
  };

  const getProductUsername = (customerProducts, productName) => {
    let productUsername = '';
    // Add null check for customerProducts
    if (customerProducts && Array.isArray(customerProducts)) {
      customerProducts.every(cp => {
        if (cp.product.name.toLowerCase() === productName.toLowerCase()) {
          productUsername = cp.username;
          return false;
        }

        return true;
      });
    }
    return productUsername;
  };

  return {
    bundleFieldValue, bundleFieldLabel, feeDisplay, computeActualFee, convertToBtc, convertFromBtc, convertToTrc20, convertFromTrc20, convertToErc20, convertFromErc20, convertToUsdc, convertFromUsdc, getProductCode, getDefaultProduct
  };
}
